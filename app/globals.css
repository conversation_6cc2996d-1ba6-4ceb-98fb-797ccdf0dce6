@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-gray-900 antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-gray-700 hover:bg-gray-100 focus:ring-primary-500;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-padding {
    @apply py-16 lg:py-24;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  .hero-text {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight;
  }
  
  .section-title {
    @apply text-3xl sm:text-4xl font-bold text-center mb-12;
  }
  
  .section-subtitle {
    @apply text-lg text-gray-600 text-center max-w-3xl mx-auto mb-16;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
  
  .animation-delay-800 {
    animation-delay: 800ms;
  }
  
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-black/20;
  }

  .text-gradient-coral {
    @apply bg-gradient-to-r from-coral-500 to-orange-500 bg-clip-text text-transparent;
  }

  .text-gradient-teal {
    @apply bg-gradient-to-r from-primary-500 to-teal-500 bg-clip-text text-transparent;
  }

  .text-gradient-rainbow {
    @apply bg-gradient-to-r from-coral-500 via-orange-500 to-teal-500 bg-clip-text text-transparent;
  }

  .btn-gradient {
    @apply bg-gradient-to-r from-coral-500 to-orange-500 hover:from-coral-600 hover:to-orange-600 text-white font-bold rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-xl;
  }

  .btn-gradient-teal {
    @apply bg-gradient-to-r from-primary-600 to-teal-600 hover:from-primary-700 hover:to-teal-700 text-white font-bold rounded-2xl transition-all duration-500 transform hover:scale-105 hover:shadow-xl;
  }

  .card-modern {
    @apply bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 border border-gray-100;
  }

  .backdrop-glass {
    @apply bg-black/20 backdrop-blur-xl border border-white/20 shadow-2xl;
  }

  /* Mobile optimizations */
  .mobile-safe-area {
    @apply pb-safe-area-inset-bottom;
  }

  .mobile-touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .mobile-scroll-container {
    @apply overflow-x-auto scrollbar-hide;
    scroll-snap-type: x mandatory;
  }

  .mobile-scroll-item {
    scroll-snap-align: start;
  }

  /* Prevent zoom on input focus on iOS */
  @media screen and (max-width: 768px) {
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="search"],
    textarea,
    select {
      font-size: 16px !important;
    }
  }

  /* Better mobile card spacing */
  @media screen and (max-width: 640px) {
    .card-modern {
      @apply mx-2 rounded-2xl;
    }

    .container-custom {
      @apply px-3;
    }
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Loading animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Smooth transitions for route changes */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}
